#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Temu商品数据批量提取实践代码
功能：
1. 新建标签页访问指定网站
2. 监听认证菜单API获取完整认证信息
3. 查询待处理商品总数并计算页数
4. 批量提取商品数据（orderId、属性集、skcId、skuId）
"""

from DrissionPage import Chromium
import json
import time
import math
from datetime import datetime

def create_new_tab_and_monitor():
    """
    创建新标签页并开始监听认证菜单API
    """
    print("🚀 开始创建新标签页并监听认证菜单API...")

    # 连接到9222端口的浏览器
    browser = Chromium(9222)
    print("✅ 已连接到浏览器（端口9222）")

    # 新建标签页
    new_tab = browser.new_tab()
    print("📄 新建空白标签页完成")

    # 激活标签页
    new_tab.set.activate()
    print("🎯 标签页已激活")
    
    # 启动网络监听器 - 监听认证API
    target_api = "https://agentseller.temu.com/api/seller/auth/menu"
    print("🔍 启动网络监听器...")
    print(f"🎯 监听目标API: {target_api}")
    new_tab.listen.start(targets="api/seller/auth/menu")
    print("✅ 网络监听器已启动，开始监听认证菜单API")
    
    # 访问目标页面
    target_url = "https://agentseller.temu.com/newon/product-select"
    print(f"🌐 正在访问目标页面：{target_url}")
    
    # 访问页面
    new_tab.get(target_url)
    
    print("⏳ 等待页面加载...")
    new_tab.wait.load_start()
    new_tab.wait.doc_loaded()
    print("✅ 页面加载完成")
    
    return new_tab

def capture_auth_info(tab):
    """
    捕获认证菜单API请求并提取认证信息
    """
    print("\n" + "=" * 80)
    print("🔍 开始监听并捕获认证菜单API请求")
    print("=" * 80)

    print("⏳ 等待认证菜单API请求...")
    print("💡 提示：如果页面已加载但未捕获到请求，请尝试刷新页面或进行操作")

    try:
        # 等待认证菜单API请求，设置较长的超时时间
        packet = tab.listen.wait(timeout=60)
        if not packet:
            print("⚠️ 未捕获到认证菜单API请求")
            return None
    except Exception as e:
        print(f"❌ 等待认证菜单API请求时发生错误：{e}")
        return None

    print("✅ 成功捕获到认证菜单API请求！")
    
    # 提取重要的认证头信息
    auth_headers = {}
    important_headers = [
        'Anti-Content', 'mallid', 'cookie', 'authorization',
        'x-csrf-token', 'x-requested-with', 'user-agent',
        'accept', 'accept-language', 'content-type',
        'origin', 'referer', 'sec-fetch-dest', 'sec-fetch-mode', 'sec-fetch-site'
    ]
    
    print("📋 正在提取认证头信息...")
    if packet.request and packet.request.headers:
        for key, value in packet.request.headers.items():
            # 检查是否是重要的认证头
            if any(important_header.lower() == key.lower() for important_header in important_headers):
                clean_key = key.strip()
                clean_value = str(value).strip()
                if clean_key and clean_value:
                    auth_headers[clean_key] = clean_value
    
    print(f"✅ 成功提取到 {len(auth_headers)} 个认证头信息")
    
    # 显示提取到的关键认证信息（不显示敏感内容）
    print("📋 提取到的认证头类型：")
    for key in auth_headers.keys():
        if key.lower() in ['cookie', 'authorization']:
            print(f"   ✅ {key}: [已获取，长度{len(auth_headers[key])}字符]")
        else:
            print(f"   ✅ {key}: {auth_headers[key][:50]}...")
    
    return auth_headers

def query_todo_count(tab, auth_headers):
    """
    查询待处理商品总数
    """
    print("\n" + "=" * 80)
    print("📊 查询待处理商品总数")
    print("=" * 80)
    
    if not auth_headers:
        print("❌ 没有认证信息，无法发起请求")
        return None
    
    api_url = "https://agentseller.temu.com/api/kiana/mms/robin/querySupplierTodoCount"
    request_data = {}
    
    print(f"🎯 API地址：{api_url}")
    print(f"📝 请求数据：{json.dumps(request_data, ensure_ascii=False)}")
    
    try:
        print("⏳ 正在查询待处理商品总数...")
        
        # 使用JavaScript发起请求
        js_code = f'''
        (async function() {{
            try {{
                const response = await fetch('{api_url}', {{
                    method: 'POST',
                    headers: {json.dumps(auth_headers)},
                    body: JSON.stringify({json.dumps(request_data)})
                }});
                
                const responseText = await response.text();
                
                const result = {{
                    status: response.status,
                    statusText: response.statusText,
                    body: responseText,
                    success: true
                }};
                
                window.todoCountResponse = result;
                return result;
            }} catch (error) {{
                const errorResult = {{
                    error: error.message,
                    success: false
                }};
                window.todoCountResponse = errorResult;
                return errorResult;
            }}
        }})();
        '''
        
        # 执行JavaScript代码
        tab.run_js('window.todoCountResponse = null;')
        tab.run_js(js_code)
        
        # 等待请求完成
        max_wait_time = 10
        wait_interval = 0.5
        
        for _ in range(int(max_wait_time / wait_interval)):
            time.sleep(wait_interval)
            try:
                response_data = tab.run_js('return window.todoCountResponse;')
                if response_data is not None:
                    break
            except Exception:
                pass
        else:
            print("❌ 请求超时")
            return None
        
        if response_data and response_data.get('success', True):
            print("✅ 查询待处理商品总数成功！")
            
            try:
                # 解析响应JSON
                response_json = json.loads(response_data['body'])
                
                # 提取总数
                total_count = response_json.get('result', {}).get('total', 0)
                print(f"📊 待处理商品总数：{total_count} 条")
                
                # 计算页数（每页50条）
                page_size = 50
                total_pages = math.ceil(total_count / page_size)
                print(f"📄 每页 {page_size} 条，共需处理 {total_pages} 页")
                
                return {
                    'total_count': total_count,
                    'page_size': page_size,
                    'total_pages': total_pages
                }
                
            except Exception as e:
                print(f"❌ 解析响应数据失败：{e}")
                print(f"原始响应：{response_data['body']}")
                return None
        else:
            print("❌ 查询待处理商品总数失败")
            if 'error' in response_data:
                print(f"错误信息：{response_data['error']}")
            return None
            
    except Exception as e:
        print(f"❌ 发起请求时发生错误：{e}")
        return None

def extract_product_data(tab, auth_headers, page_info):
    """
    批量提取商品数据
    """
    print("\n" + "=" * 80)
    print("📦 开始批量提取商品数据")
    print("=" * 80)
    
    if not auth_headers or not page_info:
        print("❌ 缺少必要信息，无法提取数据")
        return
    
    api_url = "https://agentseller.temu.com/api/kiana/mms/robin/searchForChainSupplier"
    total_pages = page_info['total_pages']
    page_size = page_info['page_size']
    
    print(f"📊 准备提取 {total_pages} 页数据，每页 {page_size} 条")
    print("=" * 80)
    
    # 只提取第一页作为示例（避免请求过多）
    for page_num in range(1, min(3, total_pages + 1)):  # 最多提取前2页作为示例
        print(f"\n📄 正在提取第 {page_num} 页数据...")
        
        request_data = {
            "pageSize": page_size,
            "pageNum": page_num,
            "supplierTodoTypeList": [6, 1]
        }
        
        try:
            # 使用JavaScript发起请求
            js_code = f'''
            (async function() {{
                try {{
                    const response = await fetch('{api_url}', {{
                        method: 'POST',
                        headers: {json.dumps(auth_headers)},
                        body: JSON.stringify({json.dumps(request_data)})
                    }});
                    
                    const responseText = await response.text();
                    
                    const result = {{
                        status: response.status,
                        statusText: response.statusText,
                        body: responseText,
                        success: true
                    }};
                    
                    window.productDataResponse = result;
                    return result;
                }} catch (error) {{
                    const errorResult = {{
                        error: error.message,
                        success: false
                    }};
                    window.productDataResponse = errorResult;
                    return errorResult;
                }}
            }})();
            '''
            
            # 执行JavaScript代码
            tab.run_js('window.productDataResponse = null;')
            tab.run_js(js_code)
            
            # 等待请求完成
            max_wait_time = 10
            wait_interval = 0.5
            
            for _ in range(int(max_wait_time / wait_interval)):
                time.sleep(wait_interval)
                try:
                    response_data = tab.run_js('return window.productDataResponse;')
                    if response_data is not None:
                        break
                except Exception:
                    pass
            else:
                print(f"❌ 第 {page_num} 页请求超时")
                continue
            
            if response_data and response_data.get('success', True):
                print(f"✅ 第 {page_num} 页数据获取成功！")
                
                try:
                    # 解析响应JSON
                    response_json = json.loads(response_data['body'])
                    data_list = response_json.get('result', {}).get('dataList', [])
                    
                    print(f"📦 第 {page_num} 页包含 {len(data_list)} 个商品项")
                    
                    # 提取每个商品的数据
                    for i, data_item in enumerate(data_list):
                        try:
                            skc_list = data_item.get('skcList', [])
                            for j, skc_item in enumerate(skc_list):
                                # 提取skcId
                                skc_id = skc_item.get('skcId', 'N/A')
                                
                                # 提取skuId
                                sku_list = skc_item.get('skuList', [])
                                sku_id = sku_list[0].get('skuId', 'N/A') if sku_list else 'N/A'
                                
                                # 提取orderId和属性集
                                supplier_price_review_list = skc_item.get('supplierPriceReviewInfoList', [])
                                if supplier_price_review_list:
                                    price_review_info = supplier_price_review_list[0]
                                    order_id = price_review_info.get('priceOrderId', 'N/A')
                                    
                                    # 提取属性集
                                    product_sku_list = price_review_info.get('productSkuList', [])
                                    if product_sku_list:
                                        product_property_list = product_sku_list[0].get('productPropertyList', [])
                                        if product_property_list:
                                            property_value = product_property_list[0].get('value', 'N/A')
                                        else:
                                            property_value = 'N/A'
                                    else:
                                        property_value = 'N/A'
                                else:
                                    order_id = 'N/A'
                                    property_value = 'N/A'
                                
                                # 打印提取的数据
                                print(f"第{page_num}页: orderId={order_id}, 属性集={property_value}, skcId={skc_id}, skuId={sku_id}")
                        
                        except Exception as e:
                            print(f"❌ 解析第 {page_num} 页商品 {i+1} 数据时出错：{e}")
                            continue
                
                except Exception as e:
                    print(f"❌ 解析第 {page_num} 页响应数据失败：{e}")
                    continue
            else:
                print(f"❌ 第 {page_num} 页数据获取失败")
                if 'error' in response_data:
                    print(f"错误信息：{response_data['error']}")
                continue
        
        except Exception as e:
            print(f"❌ 第 {page_num} 页请求时发生错误：{e}")
            continue
        
        # 页面间隔，避免请求过快
        if page_num < min(3, total_pages):
            print("⏳ 等待2秒后继续下一页...")
            time.sleep(2)

if __name__ == "__main__":
    """
    主程序：执行完整的商品数据提取流程
    """
    print("=" * 80)
    print("📚 Temu商品数据批量提取实践代码")
    print("🎯 功能：监听认证菜单API → 查询总数 → 批量提取商品数据")
    print("=" * 80)
    
    try:
        # 步骤1：创建新标签页并监听认证菜单API
        print("🚀 第一步：创建新标签页并监听认证菜单API")
        tab = create_new_tab_and_monitor()

        if tab:
            print(f"✅ 标签页创建成功！当前页面：{tab.title}")

            # 步骤2：捕获认证信息
            print("\n🚀 第二步：捕获认证菜单API信息")
            auth_headers = capture_auth_info(tab)
            
            if auth_headers:
                print("✅ 认证信息获取成功！")
                
                # 步骤3：查询待处理商品总数
                print("\n🚀 第三步：查询待处理商品总数")
                page_info = query_todo_count(tab, auth_headers)
                
                if page_info:
                    print("✅ 商品总数查询成功！")
                    
                    # 步骤4：批量提取商品数据
                    print("\n🚀 第四步：批量提取商品数据")
                    extract_product_data(tab, auth_headers, page_info)
                    
                    print("\n🎉 商品数据提取完成！")
                else:
                    print("❌ 无法获取商品总数信息")
            else:
                print("❌ 无法获取认证信息")
            
            # 停止监听器
            tab.listen.stop()
            print("\n✅ 网络监听器已停止")
        else:
            print("❌ 标签页创建失败")
        
        print("\n📋 操作总结：")
        print("   ✅ 新建标签页并访问目标网站")
        print("   ✅ 监听并获取完整认证菜单信息")
        print("   ✅ 查询待处理商品总数并计算页数")
        print("   ✅ 批量提取商品数据（orderId、属性集、skcId、skuId）")
        print("   ✅ 在控制台逐行打印提取的数据")
        
    except Exception as e:
        print(f"❌ 程序执行过程中发生错误：{e}")
        print("💡 请检查：")
        print("   1. 浏览器是否正确启动（端口9222）")
        print("   2. 网络连接是否正常")
        print("   3. 目标网址是否可访问")
        print("   4. 是否需要登录认证")
    
    print("\n" + "=" * 80)
    print("📚 Temu商品数据批量提取实践完成")
    print("💡 此代码演示了完整的认证菜单API监听、认证获取和数据提取流程")
    print("🔧 可根据实际需求调整页数范围和数据提取逻辑")
    print("=" * 80)
